import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { User, LoginRequest, LoginResponse, MFAVerifyRequest } from '@telesoft/types';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

interface AuthContextType extends AuthState {
  login: (credentials: LoginRequest) => Promise<LoginResponse>;
  verifyMFA: (request: MFAVerifyRequest) => Promise<LoginResponse>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<boolean>;
  clearError: () => void;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '/api/v1';

export function useAuth(): AuthContextType {
  const router = useRouter();
  const [state, setState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true,
    error: null,
  });

  // Get stored access token
  const getAccessToken = useCallback((): string | null => {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem('accessToken');
  }, []);

  // Set access token
  const setAccessToken = useCallback((token: string | null): void => {
    if (typeof window === 'undefined') return;
    if (token) {
      localStorage.setItem('accessToken', token);
    } else {
      localStorage.removeItem('accessToken');
    }
  }, []);

  // API request helper with auth
  const apiRequest = useCallback(async (
    endpoint: string,
    options: RequestInit = {}
  ): Promise<Response> => {
    const token = getAccessToken();
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      ...options,
      headers,
      credentials: 'include', // Include cookies for refresh token
    });

    return response;
  }, [getAccessToken]);

  // Login function
  const login = useCallback(async (credentials: LoginRequest): Promise<LoginResponse> => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const response = await apiRequest('/auth/login', {
        method: 'POST',
        body: JSON.stringify(credentials),
      });

      const result: LoginResponse = await response.json();

      if (result.success && !result.requiresMFA) {
        // Complete login
        if (result.accessToken) {
          setAccessToken(result.accessToken);
        }
        setState(prev => ({
          ...prev,
          user: result.user || null,
          isAuthenticated: true,
          isLoading: false,
          error: null,
        }));
      } else {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: result.message || 'Login failed',
        }));
      }

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Network error';
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
      return {
        success: false,
        requiresMFA: false,
        message: errorMessage,
      };
    }
  }, [apiRequest, setAccessToken]);

  // MFA verification function
  const verifyMFA = useCallback(async (request: MFAVerifyRequest): Promise<LoginResponse> => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const response = await apiRequest('/auth/mfa/verify', {
        method: 'POST',
        body: JSON.stringify(request),
      });

      const result: LoginResponse = await response.json();

      if (result.success) {
        if (result.accessToken) {
          setAccessToken(result.accessToken);
        }
        setState(prev => ({
          ...prev,
          user: result.user || null,
          isAuthenticated: true,
          isLoading: false,
          error: null,
        }));
      } else {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: result.message || 'MFA verification failed',
        }));
      }

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Network error';
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
      return {
        success: false,
        requiresMFA: false,
        message: errorMessage,
      };
    }
  }, [apiRequest, setAccessToken]);

  // Logout function
  const logout = useCallback(async (): Promise<void> => {
    try {
      await apiRequest('/auth/logout', {
        method: 'POST',
      });
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setAccessToken(null);
      setState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      });
      router.push('/login');
    }
  }, [apiRequest, setAccessToken, router]);

  // Refresh token function
  const refreshToken = useCallback(async (): Promise<boolean> => {
    try {
      const response = await apiRequest('/auth/refresh', {
        method: 'POST',
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success && result.accessToken) {
          setAccessToken(result.accessToken);
          return true;
        }
      }
    } catch (error) {
      console.error('Token refresh error:', error);
    }

    // If refresh fails, logout
    setAccessToken(null);
    setState({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
    });
    return false;
  }, [apiRequest, setAccessToken]);

  // Get user profile
  const getUserProfile = useCallback(async (): Promise<void> => {
    try {
      const response = await apiRequest('/auth/profile');

      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data) {
          setState(prev => ({
            ...prev,
            user: result.data,
            isAuthenticated: true,
            isLoading: false,
          }));
          return;
        }
      }

      // If profile fetch fails, try to refresh token
      const refreshSuccess = await refreshToken();
      if (!refreshSuccess) {
        setState(prev => ({
          ...prev,
          user: null,
          isAuthenticated: false,
          isLoading: false,
        }));
      }
    } catch (error) {
      console.error('Get profile error:', error);
      setState(prev => ({
        ...prev,
        user: null,
        isAuthenticated: false,
        isLoading: false,
      }));
    }
  }, [apiRequest, refreshToken]);

  // Clear error function
  const clearError = useCallback((): void => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // Initialize auth state on mount
  useEffect(() => {
    const token = getAccessToken();
    if (token) {
      getUserProfile();
    } else {
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, [getAccessToken, getUserProfile]);

  // Set up token refresh interval
  useEffect(() => {
    if (!state.isAuthenticated) return;

    const interval = setInterval(() => {
      refreshToken();
    }, 14 * 60 * 1000); // Refresh every 14 minutes (token expires in 15)

    return () => clearInterval(interval);
  }, [state.isAuthenticated, refreshToken]);

  return {
    ...state,
    login,
    verifyMFA,
    logout,
    refreshToken,
    clearError,
  };
}
