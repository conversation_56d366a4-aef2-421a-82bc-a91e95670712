'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { Card, CardContent, CardHeader, Button, Input } from '@telesoft/ui';
import { ThemeToggle } from './ThemeToggle';
import { useAuth } from '@/hooks/useAuth';

export function LoginForm() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showUsernameInput, setShowUsernameInput] = useState(false);
  const [selectedUser, setSelectedUser] = useState<string | null>(null);
  const [showTwoFactor, setShowTwoFactor] = useState(false);
  const [twoFactorCode, setTwoFactorCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { login, verifyMFA, error, clearError } = useAuth();

  const handleUsernameSelect = (selectedUsername: string) => {
    // Map display names to actual email addresses
    const emailMap: { [key: string]: string } = {
      'Admin': '<EMAIL>',
      'User': '<EMAIL>',
    };
    setEmail(emailMap[selectedUsername] || selectedUsername);
    setSelectedUser(selectedUsername);
    setShowUsernameInput(false);
  };

  const handleChangeUser = () => {
    setShowUsernameInput(true);
    setSelectedUser(null);
    setEmail('');
    setPassword('');
    clearError();
  };

  const handleBackToSelection = () => {
    setSelectedUser(null);
    setEmail('');
    setPassword('');
    clearError();
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    clearError();

    try {
      const result = await login({ email, password });

      if (result.success && !result.requiresMFA) {
        // Login successful, redirect to dashboard
        router.push('/dashboard');
      } else if (result.requiresMFA) {
        // Show 2FA screen
        setShowTwoFactor(true);
      }
      // Error handling is done by the useAuth hook
    } catch (error) {
      console.error('Login error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTwoFactorSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    clearError();

    try {
      const result = await verifyMFA({ email, code: twoFactorCode });

      if (result.success) {
        // MFA verification successful, redirect to dashboard
        router.push('/dashboard');
      }
      // Error handling is done by the useAuth hook
    } catch (error) {
      console.error('MFA verification error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToLogin = () => {
    setShowTwoFactor(false);
    setTwoFactorCode('');
  };

  return (
    <Card className="w-full max-w-md shadow-2xl border-border-primary/20">
      <CardHeader>
        <div className="relative">
          {/* T-Logo in top-left corner */}
          <div className="absolute top-0 left-0">
            <Image
              src="/logos/t-logo.png"
              alt="Telesoft T Logo"
              width={32}
              height={32}
              className="w-8 h-8"
            />
          </div>

          {/* Theme Toggle in top-right corner */}
          <div className="absolute top-0 right-0">
            <ThemeToggle />
          </div>

          {/* Centered title */}
          <div className="text-center space-y-2">
            <h1 className="text-2xl font-bold text-text-primary">
              IntSOC Login
            </h1>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {!showTwoFactor ? (
          /* Primary Login Form */
          <form onSubmit={handleLogin} className="space-y-6">
            <div className="space-y-4">
              {/* Username Selection/Input */}
              <div>
                {/* Fixed height label area to prevent card resize */}
                <div className="h-8 mb-4 flex items-center">
                  {!selectedUser && !showUsernameInput && (
                    <label className="block text-sm font-medium text-text-primary">
                      Select User
                    </label>
                  )}
                </div>
                {/* Fixed height container to prevent card resize */}
                <div className="h-48 flex flex-col justify-between">
                  {selectedUser ? (
                    /* Selected user focused view */
                    <div className="space-y-4">
                      <div className="flex justify-center">
                        <div className="flex flex-col items-center space-y-2">
                          {/* Selected User Avatar */}
                          <div
                            className={`w-20 h-20 rounded-full flex items-center justify-center shadow-lg ${selectedUser === 'Admin'
                              ? 'bg-gradient-to-br from-primary-500 to-primary-600'
                              : 'bg-gradient-to-br from-cyber-matrix-500 to-cyber-matrix-600'
                              }`}
                          >
                            <svg
                              className="w-10 h-10 text-white"
                              fill="currentColor"
                              viewBox="0 0 24 24"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                fillRule="evenodd"
                                d="M7.5 6a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM3.751 20.105a8.25 8.25 0 0116.498 0 .75.75 0 01-.437.695A18.683 18.683 0 0112 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 01-.437-.695z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </div>
                          <span className="text-lg font-medium text-text-primary">
                            {selectedUser}
                          </span>
                        </div>
                      </div>

                      {/* Back to user selection */}
                      <div className="text-center">
                        <Button
                          type="button"
                          variant="ghost"
                          className="text-sm text-text-secondary hover:text-text-primary"
                          onClick={handleBackToSelection}
                        >
                          ← Choose Different User
                        </Button>
                      </div>
                    </div>
                  ) : !showUsernameInput ? (
                    <div className="space-y-4">
                      {/* User Avatar Selection */}
                      <div className="flex justify-center gap-6">
                        {/* Admin User */}
                        <button
                          type="button"
                          className="flex flex-col items-center space-y-2 p-3 rounded-lg hover:bg-background-hover transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-background-primary"
                          onClick={() => handleUsernameSelect('Admin')}
                        >
                          {/* Admin Avatar Circle */}
                          <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-shadow duration-200">
                            {/* User Icon */}
                            <svg
                              className="w-8 h-8 text-white"
                              fill="currentColor"
                              viewBox="0 0 24 24"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                fillRule="evenodd"
                                d="M7.5 6a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM3.751 20.105a8.25 8.25 0 0116.498 0 .75.75 0 01-.437.695A18.683 18.683 0 0112 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 01-.437-.695z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </div>
                          {/* Username Label */}
                          <span className="text-xs font-medium text-text-primary">
                            Admin
                          </span>
                        </button>

                        {/* Auditor User */}
                        <button
                          type="button"
                          className="flex flex-col items-center space-y-2 p-3 rounded-lg hover:bg-background-hover transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-cyber-matrix-500 focus:ring-offset-2 focus:ring-offset-background-primary"
                          onClick={() => handleUsernameSelect('Auditor')}
                        >
                          {/* Auditor Avatar Circle */}
                          <div className="w-16 h-16 bg-gradient-to-br from-cyber-matrix-500 to-cyber-matrix-600 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-shadow duration-200">
                            {/* User Icon for Auditor */}
                            <svg
                              className="w-8 h-8 text-white"
                              fill="currentColor"
                              viewBox="0 0 24 24"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                fillRule="evenodd"
                                d="M7.5 6a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM3.751 20.105a8.25 8.25 0 0116.498 0 .75.75 0 01-.437.695A18.683 18.683 0 0112 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 01-.437-.695z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </div>
                          {/* Username Label */}
                          <span className="text-xs font-medium text-text-primary">
                            Auditor
                          </span>
                        </button>
                      </div>

                      {/* Change User Button */}
                      <div className="text-center">
                        <Button
                          type="button"
                          variant="ghost"
                          className="text-sm text-text-secondary hover:text-text-primary"
                          onClick={handleChangeUser}
                        >
                          Use Different Account
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {/* Custom Username Input */}
                      <div className="space-y-3">
                        <div className="flex justify-center">
                          {/* Generic User Avatar */}
                          <div className="w-16 h-16 bg-gradient-to-br from-background-tertiary to-background-hover rounded-full flex items-center justify-center shadow-lg border-2 border-border-primary">
                            <svg
                              className="w-8 h-8 text-text-secondary"
                              fill="currentColor"
                              viewBox="0 0 24 24"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                fillRule="evenodd"
                                d="M7.5 6a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM3.751 20.105a8.25 8.25 0 0116.498 0 .75.75 0 01-.437.695A18.683 18.683 0 0112 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 01-.437-.695z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </div>
                        </div>
                        <Input
                          type="email"
                          placeholder="Enter email address"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          className="w-full text-center"
                        />
                      </div>

                      <div className="text-center">
                        <Button
                          type="button"
                          variant="ghost"
                          className="text-sm text-text-secondary hover:text-text-primary"
                          onClick={() => setShowUsernameInput(false)}
                        >
                          ← Back to User Selection
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Password Input - Only show when user is selected or in custom username mode */}
              {(selectedUser || showUsernameInput) && (
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-3">
                    Password
                  </label>
                  <Input
                    type="password"
                    placeholder="Enter password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full"
                  />
                </div>
              )}
            </div>

            {/* Login Button - Only show when user is selected or in custom username mode */}
            {(selectedUser || showUsernameInput) && (
              <Button
                type="submit"
                variant="primary"
                className="w-full h-12 text-base font-medium"
                disabled={!email || !password || isLoading}
              >
                {isLoading ? 'Signing In...' : (!email || !password ? 'Enter Credentials' : 'Continue')}
              </Button>
            )}

            {/* Error Message */}
            {error && (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
                <p className="text-sm text-red-600 dark:text-red-400 text-center">{error}</p>
              </div>
            )}

            {/* Additional Options - Only show when user is selected or in custom username mode */}
            {(selectedUser || showUsernameInput) && (
              <div className="text-center pt-2">
                <Button
                  type="button"
                  variant="ghost"
                  className="text-sm text-text-secondary hover:text-text-primary"
                >
                  Forgot password?
                </Button>
              </div>
            )}
          </form>
        ) : (
          /* Two-Factor Authentication Form */
          <form onSubmit={handleTwoFactorSubmit} className="space-y-6">
            <div className="space-y-4">
              {/* User Info Display */}
              <div className="text-center space-y-3">
                <div className="flex justify-center">
                  <div
                    className={`w-16 h-16 rounded-full flex items-center justify-center shadow-lg ${selectedUser === 'Admin'
                      ? 'bg-gradient-to-br from-primary-500 to-primary-600'
                      : 'bg-gradient-to-br from-cyber-matrix-500 to-cyber-matrix-600'
                      }`}
                  >
                    <svg
                      className="w-8 h-8 text-white"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fillRule="evenodd"
                        d="M7.5 6a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM3.751 20.105a8.25 8.25 0 0116.498 0 .75.75 0 01-.437.695A18.683 18.683 0 0112 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 01-.437-.695z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                </div>
                <div>
                  <p className="text-lg font-medium text-text-primary">
                    {username}
                  </p>
                  <p className="text-sm text-text-secondary">
                    Signed in successfully
                  </p>
                </div>
              </div>

              {/* 2FA Instructions */}
              <div className="text-center space-y-2">
                <div className="flex justify-center">
                  <div className="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                    <svg
                      className="w-6 h-6 text-primary-600 dark:text-primary-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                      />
                    </svg>
                  </div>
                </div>
                <h3 className="text-lg font-semibold text-text-primary">
                  Two-Factor Authentication
                </h3>
                <p className="text-sm text-text-secondary px-4">
                  Enter the 6-digit code from your authenticator app to complete
                  sign in.
                </p>
              </div>

              {/* 2FA Code Input */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-3 text-center">
                  Verification Code
                </label>
                <Input
                  type="text"
                  placeholder="000000"
                  value={twoFactorCode}
                  onChange={(e) =>
                    setTwoFactorCode(
                      e.target.value.replace(/\D/g, '').slice(0, 6),
                    )
                  }
                  className="w-full text-center text-2xl tracking-widest font-mono"
                  maxLength={6}
                />
              </div>
            </div>

            {/* Verify Button */}
            <Button
              type="submit"
              variant="primary"
              className="w-full h-12 text-base font-medium"
              disabled={twoFactorCode.length !== 6 || isLoading}
            >
              {isLoading
                ? 'Verifying...'
                : (twoFactorCode.length !== 6
                  ? 'Enter 6-Digit Code'
                  : 'Verify & Sign In')}
            </Button>

            {/* Error Message */}
            {error && (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
                <p className="text-sm text-red-600 dark:text-red-400 text-center">{error}</p>
              </div>
            )}

            {/* Back to Login */}
            <div className="text-center">
              <Button
                type="button"
                variant="ghost"
                className="text-sm text-text-secondary hover:text-text-primary"
                onClick={handleBackToLogin}
              >
                ← Back to Login
              </Button>
            </div>
          </form>
        )}
      </CardContent>
    </Card>
  );
}
