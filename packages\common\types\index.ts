export interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'user';
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
  lastLoginAt?: Date;
  mfaEnabled: boolean;
  mfaSecret?: string;
}

export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginationParams {
  page: number;
  limit: number;
}

// Authentication Types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  requiresMFA: boolean;
  user?: Omit<User, 'mfaSecret'>;
  accessToken?: string;
  refreshToken?: string;
  message?: string;
}

export interface MFAVerifyRequest {
  email: string;
  code: string;
  tempToken?: string;
}

export interface MFASetupResponse {
  secret: string;
  qrCode: string;
  backupCodes: string[];
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface AuthSession {
  user: Omit<User, 'mfaSecret'>;
  accessToken: string;
  refreshToken: string;
  expiresAt: Date;
}

export interface PasswordResetRequest {
  email: string;
}

export interface PasswordResetConfirm {
  token: string;
  newPassword: string;
}

export interface ThreatIncident {
  incident_type: 'beaconing' | 'spike' | 'ddos' | 'outlier' | 'dga';
  risk_severity: 'info' | 'unknown' | 'low' | 'medium' | 'high' | 'critical';
  investigation_status:
  | 'created'
  | 'running'
  | 'complete'
  | 'failed'
  | 'unknown';
  time: number;
  uid: string;
  summary?: string;
  remediation_actions?: string;
  risk_message?: string;
  current_ttps?: string[];
  future_ttps?: string[];
}

export interface ThreatsResponse {
  data: {
    incidents: ThreatIncident[];
  };
  source: 'api' | 'cache';
  timestamp: string;
}

export interface Deployment {
  data: { [timestamp: string]: number };
  name: string;
  namespace: string;
  display_name?: string;
}

export interface DeploymentsResponse {
  data: {
    deployments: Deployment[];
  };
  source: 'api' | 'cache' | 'websocket';
  timestamp: string;
}
