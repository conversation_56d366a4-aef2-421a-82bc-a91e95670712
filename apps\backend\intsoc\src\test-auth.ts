// Simple test script to verify authentication functionality
import { AuthService } from './services/AuthService';

async function testAuth() {
  console.log('🔐 Testing Authentication Service...\n');
  
  const authService = new AuthService();
  
  try {
    // Test 1: Login with valid credentials
    console.log('Test 1: Login with admin credentials');
    const loginResult = await authService.login({
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    if (loginResult.success) {
      console.log('✅ Login successful');
      console.log('User:', loginResult.user?.name);
      console.log('Access Token:', loginResult.accessToken ? 'Generated' : 'Missing');
      console.log('Requires MFA:', loginResult.requiresMFA);
    } else {
      console.log('❌ Login failed:', loginResult.message);
    }
    
    console.log('\n---\n');
    
    // Test 2: Login with invalid credentials
    console.log('Test 2: Login with invalid credentials');
    const invalidLogin = await authService.login({
      email: '<EMAIL>',
      password: 'wrongpassword'
    });
    
    if (!invalidLogin.success) {
      console.log('✅ Invalid login correctly rejected');
    } else {
      console.log('❌ Invalid login incorrectly accepted');
    }
    
    console.log('\n---\n');
    
    // Test 3: Token validation
    if (loginResult.accessToken) {
      console.log('Test 3: Token validation');
      const user = await authService.validateToken(loginResult.accessToken);
      
      if (user) {
        console.log('✅ Token validation successful');
        console.log('User from token:', user.name);
      } else {
        console.log('❌ Token validation failed');
      }
    }
    
    console.log('\n---\n');
    
    // Test 4: MFA Setup
    console.log('Test 4: MFA Setup');
    const users = await authService.getAllUsers();
    const adminUser = users.find(u => u.email === '<EMAIL>');
    
    if (adminUser) {
      const mfaSetup = await authService.setupMFA(adminUser.id);
      console.log('✅ MFA setup successful');
      console.log('Secret generated:', mfaSetup.secret ? 'Yes' : 'No');
      console.log('QR Code generated:', mfaSetup.qrCode ? 'Yes' : 'No');
      console.log('Backup codes generated:', mfaSetup.backupCodes.length);
    }
    
    console.log('\n🎉 All tests completed!');
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Run the test
testAuth().catch(console.error);
