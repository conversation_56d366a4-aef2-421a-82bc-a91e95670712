import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import speakeasy from 'speakeasy';
import QRCode from 'qrcode';
import crypto from 'crypto';
import { User, LoginRequest, LoginResponse, MFAVerifyRequest, MFASetupResponse, AuthSession } from '@telesoft/types';

interface UserStore {
  [email: string]: User & { passwordHash: string };
}

export class AuthService {
  private users: UserStore = {};
  private sessions: Map<string, AuthSession> = new Map();
  private refreshTokens: Map<string, { userId: string; expiresAt: Date }> = new Map();
  private resetTokens: Map<string, { userId: string; expiresAt: Date }> = new Map();
  
  private readonly JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';
  private readonly JWT_EXPIRES_IN = '15m';
  private readonly REFRESH_TOKEN_EXPIRES_IN = '7d';
  private readonly RESET_TOKEN_EXPIRES_IN = '1h';

  constructor() {
    this.initializeDefaultUsers();
  }

  private initializeDefaultUsers() {
    // Create default admin user
    const adminPasswordHash = bcrypt.hashSync('admin123', 12);
    this.users['<EMAIL>'] = {
      id: 'admin-001',
      email: '<EMAIL>',
      name: 'System Administrator',
      role: 'admin',
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true,
      mfaEnabled: false,
      passwordHash: adminPasswordHash,
    };

    // Create default user
    const userPasswordHash = bcrypt.hashSync('user123', 12);
    this.users['<EMAIL>'] = {
      id: 'user-001',
      email: '<EMAIL>',
      name: 'Regular User',
      role: 'user',
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true,
      mfaEnabled: false,
      passwordHash: userPasswordHash,
    };

    console.log('AuthService: Initialized with default users');
    console.log('Admin: <EMAIL> / admin123');
    console.log('User: <EMAIL> / user123');
  }

  async login(loginRequest: LoginRequest): Promise<LoginResponse> {
    const { email, password } = loginRequest;
    
    const user = this.users[email.toLowerCase()];
    if (!user || !user.isActive) {
      return {
        success: false,
        requiresMFA: false,
        message: 'Invalid credentials',
      };
    }

    const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
    if (!isPasswordValid) {
      return {
        success: false,
        requiresMFA: false,
        message: 'Invalid credentials',
      };
    }

    // Update last login
    user.lastLoginAt = new Date();
    user.updatedAt = new Date();

    // If MFA is enabled, return temporary response
    if (user.mfaEnabled) {
      const tempToken = this.generateTempToken(user.id);
      return {
        success: true,
        requiresMFA: true,
        message: 'MFA verification required',
      };
    }

    // Generate tokens and create session
    const { accessToken, refreshToken } = this.generateTokens(user);
    const session = this.createSession(user, accessToken, refreshToken);

    return {
      success: true,
      requiresMFA: false,
      user: this.sanitizeUser(user),
      accessToken,
      refreshToken,
      message: 'Login successful',
    };
  }

  async verifyMFA(request: MFAVerifyRequest): Promise<LoginResponse> {
    const { email, code } = request;
    
    const user = this.users[email.toLowerCase()];
    if (!user || !user.isActive || !user.mfaEnabled || !user.mfaSecret) {
      return {
        success: false,
        requiresMFA: false,
        message: 'Invalid MFA verification',
      };
    }

    const isValidCode = speakeasy.totp.verify({
      secret: user.mfaSecret,
      encoding: 'base32',
      token: code,
      window: 2, // Allow 2 time steps (60 seconds) of tolerance
    });

    if (!isValidCode) {
      return {
        success: false,
        requiresMFA: true,
        message: 'Invalid MFA code',
      };
    }

    // Generate tokens and create session
    const { accessToken, refreshToken } = this.generateTokens(user);
    const session = this.createSession(user, accessToken, refreshToken);

    return {
      success: true,
      requiresMFA: false,
      user: this.sanitizeUser(user),
      accessToken,
      refreshToken,
      message: 'MFA verification successful',
    };
  }

  async setupMFA(userId: string): Promise<MFASetupResponse> {
    const user = Object.values(this.users).find(u => u.id === userId);
    if (!user) {
      throw new Error('User not found');
    }

    const secret = speakeasy.generateSecret({
      name: `IntSOC (${user.email})`,
      issuer: 'IntSOC Security Platform',
    });

    // Generate QR code
    const qrCode = await QRCode.toDataURL(secret.otpauth_url!);

    // Generate backup codes
    const backupCodes = Array.from({ length: 8 }, () => 
      crypto.randomBytes(4).toString('hex').toUpperCase()
    );

    // Store the secret (but don't enable MFA yet)
    user.mfaSecret = secret.base32;

    return {
      secret: secret.base32!,
      qrCode,
      backupCodes,
    };
  }

  async enableMFA(userId: string, verificationCode: string): Promise<boolean> {
    const user = Object.values(this.users).find(u => u.id === userId);
    if (!user || !user.mfaSecret) {
      return false;
    }

    const isValidCode = speakeasy.totp.verify({
      secret: user.mfaSecret,
      encoding: 'base32',
      token: verificationCode,
      window: 2,
    });

    if (isValidCode) {
      user.mfaEnabled = true;
      user.updatedAt = new Date();
      return true;
    }

    return false;
  }

  async disableMFA(userId: string, verificationCode: string): Promise<boolean> {
    const user = Object.values(this.users).find(u => u.id === userId);
    if (!user || !user.mfaEnabled || !user.mfaSecret) {
      return false;
    }

    const isValidCode = speakeasy.totp.verify({
      secret: user.mfaSecret,
      encoding: 'base32',
      token: verificationCode,
      window: 2,
    });

    if (isValidCode) {
      user.mfaEnabled = false;
      user.mfaSecret = undefined;
      user.updatedAt = new Date();
      return true;
    }

    return false;
  }

  async refreshToken(refreshToken: string): Promise<{ accessToken: string; refreshToken: string } | null> {
    const tokenData = this.refreshTokens.get(refreshToken);
    if (!tokenData || tokenData.expiresAt < new Date()) {
      this.refreshTokens.delete(refreshToken);
      return null;
    }

    const user = Object.values(this.users).find(u => u.id === tokenData.userId);
    if (!user || !user.isActive) {
      this.refreshTokens.delete(refreshToken);
      return null;
    }

    // Generate new tokens
    const newTokens = this.generateTokens(user);
    
    // Remove old refresh token and add new one
    this.refreshTokens.delete(refreshToken);
    
    return newTokens;
  }

  async logout(accessToken: string): Promise<boolean> {
    try {
      const decoded = jwt.verify(accessToken, this.JWT_SECRET) as any;
      const sessionId = decoded.sessionId;
      
      if (sessionId) {
        this.sessions.delete(sessionId);
      }
      
      return true;
    } catch (error) {
      return false;
    }
  }

  async validateToken(token: string): Promise<User | null> {
    try {
      const decoded = jwt.verify(token, this.JWT_SECRET) as any;
      const user = Object.values(this.users).find(u => u.id === decoded.userId);
      
      if (!user || !user.isActive) {
        return null;
      }

      return this.sanitizeUser(user);
    } catch (error) {
      return null;
    }
  }

  private generateTokens(user: User): { accessToken: string; refreshToken: string } {
    const sessionId = crypto.randomUUID();
    
    const accessToken = jwt.sign(
      { 
        userId: user.id, 
        email: user.email, 
        role: user.role,
        sessionId 
      },
      this.JWT_SECRET,
      { expiresIn: this.JWT_EXPIRES_IN }
    );

    const refreshToken = crypto.randomBytes(32).toString('hex');
    const refreshExpiresAt = new Date();
    refreshExpiresAt.setDate(refreshExpiresAt.getDate() + 7); // 7 days

    this.refreshTokens.set(refreshToken, {
      userId: user.id,
      expiresAt: refreshExpiresAt,
    });

    return { accessToken, refreshToken };
  }

  private createSession(user: User, accessToken: string, refreshToken: string): AuthSession {
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + 15); // 15 minutes

    const session: AuthSession = {
      user: this.sanitizeUser(user),
      accessToken,
      refreshToken,
      expiresAt,
    };

    const sessionId = crypto.randomUUID();
    this.sessions.set(sessionId, session);

    return session;
  }

  private generateTempToken(userId: string): string {
    return jwt.sign({ userId, temp: true }, this.JWT_SECRET, { expiresIn: '5m' });
  }

  private sanitizeUser(user: User): Omit<User, 'mfaSecret'> {
    const { mfaSecret, ...sanitized } = user;
    return sanitized;
  }

  // Admin methods for user management
  async createUser(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'> & { password: string }): Promise<User> {
    const { password, ...userInfo } = userData;
    const passwordHash = await bcrypt.hash(password, 12);
    
    const user: User & { passwordHash: string } = {
      ...userInfo,
      id: crypto.randomUUID(),
      createdAt: new Date(),
      updatedAt: new Date(),
      passwordHash,
    };

    this.users[user.email.toLowerCase()] = user;
    return this.sanitizeUser(user);
  }

  async getAllUsers(): Promise<Omit<User, 'mfaSecret'>[]> {
    return Object.values(this.users).map(user => this.sanitizeUser(user));
  }

  async getUserById(id: string): Promise<Omit<User, 'mfaSecret'> | null> {
    const user = Object.values(this.users).find(u => u.id === id);
    return user ? this.sanitizeUser(user) : null;
  }
}
