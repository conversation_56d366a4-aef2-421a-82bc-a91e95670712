# Server Configuration
NODE_ENV=development
PORT=4001
DOMAIN=localhost

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production-make-it-long-and-random

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_CONNECT_TIMEOUT=2000
REDIS_COMMAND_TIMEOUT=1000

# External API Configuration
EXTERNAL_THREATS_API=
EXTERNAL_THREATS_WS=
EXTERNAL_ML_API=
EXTERNAL_ML_WS=
