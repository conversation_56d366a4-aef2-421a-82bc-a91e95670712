import { Request, Response, Router } from 'express';
import { AuthService } from '../services/AuthService';
import { LoginRequest, MFAVerifyRequest, RefreshTokenRequest } from '@telesoft/types';

export default class AuthController {
  private authService: AuthService;

  constructor(router: Router) {
    this.authService = new AuthService();
    this.initializeRoutes(router);
  }

  private initializeRoutes(router: Router): void {
    // Authentication routes
    router.post('/auth/login', this.login.bind(this));
    router.post('/auth/mfa/verify', this.verifyMFA.bind(this));
    router.post('/auth/refresh', this.refreshToken.bind(this));
    router.post('/auth/logout', this.logout.bind(this));
    
    // MFA management routes
    router.get('/auth/mfa/setup', this.authenticateToken.bind(this), this.setupMFA.bind(this));
    router.post('/auth/mfa/enable', this.authenticateToken.bind(this), this.enableMFA.bind(this));
    router.post('/auth/mfa/disable', this.authenticateToken.bind(this), this.disableMFA.bind(this));
    
    // User profile routes
    router.get('/auth/profile', this.authenticateToken.bind(this), this.getProfile.bind(this));
    router.get('/auth/users', this.authenticateToken.bind(this), this.requireAdmin.bind(this), this.getAllUsers.bind(this));
    
    // Health check for auth service
    router.get('/auth/health', this.healthCheck.bind(this));
  }

  private async login(req: Request, res: Response): Promise<void> {
    try {
      const loginRequest: LoginRequest = req.body;
      
      if (!loginRequest.email || !loginRequest.password) {
        res.status(400).json({
          success: false,
          message: 'Email and password are required',
        });
        return;
      }

      const result = await this.authService.login(loginRequest);
      
      if (!result.success) {
        res.status(401).json(result);
        return;
      }

      // Set refresh token as httpOnly cookie if login is complete
      if (!result.requiresMFA && result.refreshToken) {
        res.cookie('refreshToken', result.refreshToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict',
          maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
        });
      }

      res.status(200).json(result);
    } catch (error) {
      console.error('Login error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }

  private async verifyMFA(req: Request, res: Response): Promise<void> {
    try {
      const mfaRequest: MFAVerifyRequest = req.body;
      
      if (!mfaRequest.email || !mfaRequest.code) {
        res.status(400).json({
          success: false,
          message: 'Email and MFA code are required',
        });
        return;
      }

      const result = await this.authService.verifyMFA(mfaRequest);
      
      if (!result.success) {
        res.status(401).json(result);
        return;
      }

      // Set refresh token as httpOnly cookie
      if (result.refreshToken) {
        res.cookie('refreshToken', result.refreshToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict',
          maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
        });
      }

      res.status(200).json(result);
    } catch (error) {
      console.error('MFA verification error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }

  private async refreshToken(req: Request, res: Response): Promise<void> {
    try {
      const refreshToken = req.cookies.refreshToken || req.body.refreshToken;
      
      if (!refreshToken) {
        res.status(401).json({
          success: false,
          message: 'Refresh token required',
        });
        return;
      }

      const result = await this.authService.refreshToken(refreshToken);
      
      if (!result) {
        res.status(401).json({
          success: false,
          message: 'Invalid or expired refresh token',
        });
        return;
      }

      // Set new refresh token as httpOnly cookie
      res.cookie('refreshToken', result.refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      });

      res.status(200).json({
        success: true,
        accessToken: result.accessToken,
      });
    } catch (error) {
      console.error('Token refresh error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }

  private async logout(req: Request, res: Response): Promise<void> {
    try {
      const authHeader = req.headers.authorization;
      const token = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null;
      
      if (token) {
        await this.authService.logout(token);
      }

      // Clear refresh token cookie
      res.clearCookie('refreshToken');
      
      res.status(200).json({
        success: true,
        message: 'Logged out successfully',
      });
    } catch (error) {
      console.error('Logout error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }

  private async setupMFA(req: Request, res: Response): Promise<void> {
    try {
      const userId = (req as any).user.id;
      const result = await this.authService.setupMFA(userId);
      
      res.status(200).json({
        success: true,
        data: result,
      });
    } catch (error) {
      console.error('MFA setup error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to setup MFA',
      });
    }
  }

  private async enableMFA(req: Request, res: Response): Promise<void> {
    try {
      const userId = (req as any).user.id;
      const { code } = req.body;
      
      if (!code) {
        res.status(400).json({
          success: false,
          message: 'Verification code is required',
        });
        return;
      }

      const success = await this.authService.enableMFA(userId, code);
      
      if (success) {
        res.status(200).json({
          success: true,
          message: 'MFA enabled successfully',
        });
      } else {
        res.status(400).json({
          success: false,
          message: 'Invalid verification code',
        });
      }
    } catch (error) {
      console.error('Enable MFA error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to enable MFA',
      });
    }
  }

  private async disableMFA(req: Request, res: Response): Promise<void> {
    try {
      const userId = (req as any).user.id;
      const { code } = req.body;
      
      if (!code) {
        res.status(400).json({
          success: false,
          message: 'Verification code is required',
        });
        return;
      }

      const success = await this.authService.disableMFA(userId, code);
      
      if (success) {
        res.status(200).json({
          success: true,
          message: 'MFA disabled successfully',
        });
      } else {
        res.status(400).json({
          success: false,
          message: 'Invalid verification code',
        });
      }
    } catch (error) {
      console.error('Disable MFA error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to disable MFA',
      });
    }
  }

  private async getProfile(req: Request, res: Response): Promise<void> {
    try {
      const user = (req as any).user;
      res.status(200).json({
        success: true,
        data: user,
      });
    } catch (error) {
      console.error('Get profile error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get profile',
      });
    }
  }

  private async getAllUsers(req: Request, res: Response): Promise<void> {
    try {
      const users = await this.authService.getAllUsers();
      res.status(200).json({
        success: true,
        data: users,
      });
    } catch (error) {
      console.error('Get users error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get users',
      });
    }
  }

  private async healthCheck(req: Request, res: Response): Promise<void> {
    res.status(200).json({
      success: true,
      message: 'Auth service is healthy',
      timestamp: new Date().toISOString(),
    });
  }

  // Middleware for token authentication
  private async authenticateToken(req: Request, res: Response, next: any): Promise<void> {
    try {
      const authHeader = req.headers.authorization;
      const token = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null;
      
      if (!token) {
        res.status(401).json({
          success: false,
          message: 'Access token required',
        });
        return;
      }

      const user = await this.authService.validateToken(token);
      
      if (!user) {
        res.status(401).json({
          success: false,
          message: 'Invalid or expired token',
        });
        return;
      }

      (req as any).user = user;
      next();
    } catch (error) {
      console.error('Token authentication error:', error);
      res.status(401).json({
        success: false,
        message: 'Invalid token',
      });
    }
  }

  // Middleware for admin role requirement
  private async requireAdmin(req: Request, res: Response, next: any): Promise<void> {
    const user = (req as any).user;
    
    if (user.role !== 'admin') {
      res.status(403).json({
        success: false,
        message: 'Admin access required',
      });
      return;
    }

    next();
  }
}
